from app import db, app
from models import User, CampusCard
from werkzeug.security import generate_password_hash  # 添加密码加密

def create_test_data():
    print("开始创建测试数据...")
    
    with app.app_context():
        # 检查是否已有数据
        if User.query.count() > 0:
            print("数据库已有数据，跳过初始化")
            return
            
        print("创建测试用户...")
        # 创建测试用户 - 使用加密密码
        users = [
            User(
                student_id="20210001", 
                full_name="张三", 
                password=generate_password_hash("pass123")
            ),
            User(
                student_id="20210002", 
                full_name="李四", 
                password=generate_password_hash("pass456")
            ),
            User(
                student_id="20210003", 
                full_name="王五", 
                password=generate_password_hash("pass789")
            )
        ]
        
        print("创建测试校园卡...")
        # 创建测试校园卡 - 每个用户对应一张卡
        cards = [
            CampusCard(
                card_number="20210001", 
                student_id="20210001",  # 与用户1关联
                status="normal"
            ),
            CampusCard(
                card_number="20210002", 
                student_id="20210002",  # 与用户2关联
                status="normal"
            ),
            CampusCard(
                card_number="20210003", 
                student_id="20210003",  # 与用户3关联
                status="normal"
            )
        ]
        
        try:
            # 批量添加
            db.session.add_all(users)
            db.session.add_all(cards)
            db.session.commit()
            print(f"成功创建 {len(users)} 个用户和 {len(cards)} 张校园卡")
            print("测试数据初始化完成")
        except Exception as e:
            db.session.rollback()
            print(f"创建测试数据失败: {str(e)}")
        finally:
            db.session.close()

if __name__ == "__main__":
    create_test_data()