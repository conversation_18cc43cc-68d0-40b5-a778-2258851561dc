// 全局变量
let currentUser = null;
const API_BASE = 'http://localhost:5000';

// 连接保持机制
let keepAliveInterval = null;
let connectionStatus = 'connected';

// 页面切换函数
function showPage(pageId) {
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    document.getElementById(pageId).classList.add('active');
}

function showRegister() {
    showPage('register-page');
}

function showLogin() {
    showPage('login-page');
}

function showHome() {
    showPage('home-page');
    updateUserInfo();
}

function showReportCard() {
    showPage('report-card-page');
}

function showQueryCard() {
    showPage('query-card-page');
    updateHistoryDisplay(); // 显示查询历史
    document.getElementById('query-student-id').focus(); // 自动聚焦到输入框
}

function showPublicList() {
    showPage('public-list-page');
    loadHotLocations(); // 加载热门地点
    loadPublicList();   // 加载公示列表
}

function showForum() {
    showPage('forum-page');
    loadForumPosts();
}

function showCreatePost() {
    showPage('create-post-page');
}

function showRewards() {
    showPage('rewards-page');
    loadRewards();
}

function showSmartLocationQuery() {
    showPage('smart-location-page');
    document.getElementById('location-input').focus(); // 自动聚焦到输入框
}

function logout() {
    currentUser = null;
    showLogin();
}

// 连接保持函数
async function keepAlive() {
    try {
        const response = await fetch(`${API_BASE}/ping`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            timeout: 5000
        });

        if (response.ok) {
            connectionStatus = 'connected';
            updateConnectionStatus();
        } else {
            throw new Error('Ping failed');
        }
    } catch (error) {
        console.warn('连接保持失败:', error);
        connectionStatus = 'disconnected';
        updateConnectionStatus();
    }
}

// 更新连接状态显示
function updateConnectionStatus() {
    // 可以在这里添加UI状态指示器
    if (connectionStatus === 'disconnected') {
        console.warn('与服务器连接中断，正在尝试重连...');
    }
}

// 启动连接保持
function startKeepAlive() {
    if (keepAliveInterval) {
        clearInterval(keepAliveInterval);
    }
    // 每30秒ping一次服务器
    keepAliveInterval = setInterval(keepAlive, 30000);
    console.log('连接保持机制已启动');
}

// 停止连接保持
function stopKeepAlive() {
    if (keepAliveInterval) {
        clearInterval(keepAliveInterval);
        keepAliveInterval = null;
        console.log('连接保持机制已停止');
    }
}

// API 调用函数（增强版）
async function apiCall(endpoint, method = 'GET', data = null, retries = 3) {
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
        },
    };

    if (data) {
        options.body = JSON.stringify(data);
    }

    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            const response = await fetch(`${API_BASE}${endpoint}`, options);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'API调用失败');
            }

            // 成功时更新连接状态
            connectionStatus = 'connected';
            updateConnectionStatus();

            return result;
        } catch (error) {
            console.error(`API调用错误 (尝试 ${attempt}/${retries}):`, error);

            if (attempt === retries) {
                connectionStatus = 'disconnected';
                updateConnectionStatus();
                alert('操作失败: ' + error.message + '\n请检查网络连接或刷新页面重试');
                throw error;
            }

            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
    }
}

// 注册功能
document.getElementById('register-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const studentId = document.getElementById('reg-student-id').value;
    const fullName = document.getElementById('reg-full-name').value;
    const password = document.getElementById('reg-password').value;
    
    try {
        await apiCall('/register', 'POST', {
            student_id: studentId,
            full_name: fullName,
            password: password
        });
        
        alert('注册成功！请登录');
        showLogin();
        
        // 清空表单
        document.getElementById('register-form').reset();
    } catch (error) {
        // 错误已在apiCall中处理
    }
});

// 登录功能
document.getElementById('login-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const studentId = document.getElementById('login-student-id').value;
    const fullName = document.getElementById('login-full-name').value;
    const password = document.getElementById('login-password').value;
    
    try {
        const result = await apiCall('/login', 'POST', {
            student_id: studentId,
            full_name: fullName,
            password: password
        });
        
        currentUser = result;
        alert('登录成功！');
        showHome();
        
        // 清空表单
        document.getElementById('login-form').reset();
    } catch (error) {
        // 错误已在apiCall中处理
    }
});

// 更新用户信息显示
function updateUserInfo() {
    if (currentUser) {
        document.getElementById('user-name').textContent = `欢迎，${currentUser.full_name}`;
        document.getElementById('user-points').textContent = `积分：${currentUser.points}`;
        document.getElementById('current-points').textContent = currentUser.points;
    }
}

// 图片预览功能
document.getElementById('photo-upload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const previewDiv = document.getElementById('photo-preview');

    if (file) {
        // 检查文件类型
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件');
            e.target.value = '';
            return;
        }

        // 检查文件大小 (16MB)
        if (file.size > 16 * 1024 * 1024) {
            alert('图片文件不能超过16MB');
            e.target.value = '';
            return;
        }

        // 创建预览
        const reader = new FileReader();
        reader.onload = function(e) {
            previewDiv.innerHTML = `
                <img src="${e.target.result}" alt="预览图片">
                <div class="preview-info">文件名: ${file.name} (${(file.size / 1024).toFixed(1)} KB)</div>
                <button type="button" class="remove-photo" onclick="removePhoto()">移除图片</button>
            `;
        };
        reader.readAsDataURL(file);
    } else {
        previewDiv.innerHTML = '';
    }
});

// 移除图片功能
function removePhoto() {
    document.getElementById('photo-upload').value = '';
    document.getElementById('photo-preview').innerHTML = '';
}

// 切换联系方式字段显示
function toggleContactField() {
    const handlerOption = document.querySelector('input[name="handler-option"]:checked');
    const contactField = document.getElementById('contact-field');
    const contactInput = document.getElementById('contact-phone');
    const pickupLocationField = document.getElementById('pickup-location-field');
    const pickupLocationSelect = document.getElementById('pickup-location');

    if (handlerOption && handlerOption.value === '1') {
        // 选择自行联系，显示联系方式字段，隐藏地点选择
        contactField.style.display = 'block';
        pickupLocationField.style.display = 'none';
        contactInput.required = true;
        pickupLocationSelect.required = false;
        pickupLocationSelect.value = '';
        contactInput.focus();
    } else if (handlerOption && handlerOption.value === '2') {
        // 选择放置地点，显示地点选择字段，隐藏联系方式
        contactField.style.display = 'none';
        pickupLocationField.style.display = 'block';
        contactInput.required = false;
        pickupLocationSelect.required = true;
        contactInput.value = '';
        pickupLocationSelect.focus();
    } else {
        // 未选择，隐藏所有字段
        contactField.style.display = 'none';
        pickupLocationField.style.display = 'none';
        contactInput.required = false;
        pickupLocationSelect.required = false;
        contactInput.value = '';
        pickupLocationSelect.value = '';
    }
}

// 报告校园卡功能
document.getElementById('report-card-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    // 检查用户是否已登录
    if (!currentUser) {
        alert('请先登录后再提交捡卡信息');
        showLogin();
        return;
    }

    const cardNumber = document.getElementById('card-number').value;
    const foundLocation = document.getElementById('found-location').value;
    const handlerOption = document.querySelector('input[name="handler-option"]:checked').value;
    const contactPhone = document.getElementById('contact-phone').value;
    const pickupLocation = document.getElementById('pickup-location').value;
    const photoFile = document.getElementById('photo-upload').files[0];

    // 验证联系方式
    if (handlerOption === '1' && !contactPhone.trim()) {
        alert('选择"自行联系失主"时必须提供联系方式');
        document.getElementById('contact-phone').focus();
        return;
    }

    // 验证拾取点选择
    if (handlerOption === '2' && !pickupLocation) {
        alert('选择"放置到指定地点"时必须选择拾取点');
        document.getElementById('pickup-location').focus();
        return;
    }

    try {
        // 创建FormData对象来支持文件上传
        const formData = new FormData();
        formData.append('card_number', cardNumber);
        formData.append('found_location', foundLocation);
        formData.append('handler_option', handlerOption);
        // 添加当前登录用户信息用于积分奖励
        formData.append('current_user_id', currentUser.user_id);
        formData.append('current_user_name', currentUser.full_name);

        // 根据处理方式添加相应的contact信息
        if (handlerOption === '1' && contactPhone.trim()) {
            // 自行联系：添加联系方式
            formData.append('contact', contactPhone.trim());
        } else if (handlerOption === '2' && pickupLocation) {
            // 放置指定地点：添加拾取点信息
            formData.append('contact', pickupLocation);
        }

        if (photoFile) {
            formData.append('photo', photoFile);
        }

        // 使用fetch直接发送FormData
        const response = await fetch(`${API_BASE}/report_card`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || '提交失败');
        }

        // 如果获得了积分奖励，更新当前用户的积分
        if (result.points_awarded && result.points_awarded > 0) {
            currentUser.points += result.points_awarded;
            updateUserInfo(); // 更新界面显示的积分
        }

        alert(result.message);
        document.getElementById('report-card-form').reset();
        document.getElementById('photo-preview').innerHTML = '';
        // 隐藏所有动态字段
        document.getElementById('contact-field').style.display = 'none';
        document.getElementById('pickup-location-field').style.display = 'none';
        showHome();
    } catch (error) {
        console.error('提交错误:', error);
        alert('操作失败: ' + error.message);
    }
});

// 查询历史存储
let queryHistory = JSON.parse(localStorage.getItem('queryHistory') || '[]');

// 显示查询状态
function showQueryStatus(type, message) {
    const statusDiv = document.getElementById('query-status');
    statusDiv.className = `query-status ${type}`;
    statusDiv.textContent = message;

    if (type !== 'loading') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }
}

// 添加查询历史
function addToHistory(studentId, result) {
    const historyItem = {
        studentId: studentId,
        timestamp: new Date().toLocaleString(),
        result: result.status === 'found' ? '找到校园卡' : '未找到校园卡',
        fullResult: result
    };

    // 避免重复添加相同的查询
    queryHistory = queryHistory.filter(item => item.studentId !== studentId);
    queryHistory.unshift(historyItem);

    // 只保留最近10条记录
    if (queryHistory.length > 10) {
        queryHistory = queryHistory.slice(0, 10);
    }

    localStorage.setItem('queryHistory', JSON.stringify(queryHistory));
    updateHistoryDisplay();
}

// 更新历史显示
function updateHistoryDisplay() {
    const historySection = document.getElementById('query-history-section');
    const historyDiv = document.getElementById('query-history');

    if (queryHistory.length > 0) {
        historySection.style.display = 'block';
        historyDiv.innerHTML = '';

        queryHistory.forEach(item => {
            historyDiv.innerHTML += `
                <div class="history-item" onclick="replayQuery('${item.studentId}')">
                    <div class="history-query">学号: ${item.studentId}</div>
                    <div class="history-time">${item.timestamp}</div>
                    <div class="history-result">${item.result}</div>
                </div>
            `;
        });
    } else {
        historySection.style.display = 'none';
    }
}

// 重新执行查询
function replayQuery(studentId) {
    document.getElementById('query-student-id').value = studentId;
    document.getElementById('query-card-form').dispatchEvent(new Event('submit'));
}

// 清空查询
function clearQuery() {
    document.getElementById('query-student-id').value = '';
    document.getElementById('query-result').innerHTML = '';
    document.getElementById('query-status').style.display = 'none';
    document.getElementById('query-student-id').focus();
}

// 清空历史
function clearHistory() {
    if (confirm('确定要清空查询历史吗？')) {
        queryHistory = [];
        localStorage.removeItem('queryHistory');
        updateHistoryDisplay();
    }
}

// 查询校园卡功能
document.getElementById('query-card-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    const studentId = document.getElementById('query-student-id').value.trim();

    if (!studentId) {
        showQueryStatus('error', '请输入学号');
        return;
    }

    // 显示加载状态
    showQueryStatus('loading', '正在查询...');

    try {
        const result = await apiCall(`/query_lost_card?student_id=${studentId}`);

        const resultDiv = document.getElementById('query-result');

        if (result.status === 'found') {
            let contactInfoHtml = '';

            if (result.handler_type === 'contact' && result.contact_info) {
                // 自行联系：显示联系方式
                contactInfoHtml = `
                    <div class="contact-info">
                        <div class="contact-label">📞 拾卡者联系方式：</div>
                        <div class="contact-value">${result.contact_info}</div>
                    </div>
                `;
            } else if (result.handler_type === 'location' && result.location_info) {
                // 放置地点：显示地点信息
                contactInfoHtml = `
                    <div class="location-info">
                        <div class="location-label">📍 领取地点：</div>
                        <div class="location-value">${result.location_info}</div>
                    </div>
                `;
            }

            resultDiv.innerHTML = `
                <div class="result-item">
                    <h4>🎉 好消息！</h4>
                    <p>${result.message}</p>
                    ${contactInfoHtml}
                </div>
            `;
            showQueryStatus('success', '查询成功！找到了您的校园卡');
        } else {
            resultDiv.innerHTML = `
                <div class="result-item">
                    <h4>📋 查询结果</h4>
                    <p>${result.message}</p>
                </div>
            `;

            if (result.unmatched_cards && result.unmatched_cards.length > 0) {
                resultDiv.innerHTML += '<h4>🔍 未匹配的校园卡：</h4>';
                result.unmatched_cards.forEach(card => {
                    // 根据是否有真实姓名显示不同的信息
                    let ownerInfo = '';
                    if (card.owner_name && card.name_source === 'real') {
                        ownerInfo = `<strong>卡主姓名：</strong>${card.owner_name} (${card.masked_info.student_id})`;
                    } else {
                        ownerInfo = `<strong>持卡人信息：</strong>${card.masked_info.name} (${card.masked_info.student_id})`;
                    }

                    resultDiv.innerHTML += `
                        <div class="result-item">
                            <p><strong>发现时间：</strong>${card.found_time}</p>
                            <p><strong>发现地点：</strong>${card.found_location}</p>
                            <p>${ownerInfo}</p>
                        </div>
                    `;
                });
            }
            showQueryStatus('success', '查询完成');
        }

        // 添加到查询历史
        addToHistory(studentId, result);

        // 滚动到结果区域
        document.getElementById('query-result-section').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

    } catch (error) {
        showQueryStatus('error', '查询失败，请稍后重试');
        document.getElementById('query-result').innerHTML = `
            <div class="result-item">
                <h4>❌ 查询失败</h4>
                <p>网络错误或服务器异常，请稍后重试</p>
            </div>
        `;
    }
});

// 全局变量存储图表实例和数据
let locationsChart = null;
let locationsData = [];
let currentChartType = 'grid';

// 加载热门地点
async function loadHotLocations() {
    try {
        const response = await apiCall('/hot_locations');

        // 处理新的响应格式
        let locations, statistics;
        if (response.locations) {
            // 新格式：包含locations数组和statistics对象
            locations = response.locations;
            statistics = response.statistics;
        } else {
            // 兼容旧格式：直接是locations数组
            locations = response;
            statistics = null;
        }

        locationsData = locations; // 保存数据供图表使用

        // 更新卡片视图
        updateGridView(locations, statistics);

        // 如果当前是图表视图，更新图表
        if (currentChartType !== 'grid') {
            updateChart(currentChartType);
        }

    } catch (error) {
        document.getElementById('hot-locations-content').innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">❌</div>
                <div class="empty-text">加载失败</div>
                <div class="empty-hint">请稍后重试</div>
            </div>
        `;
    }
}

// 更新卡片网格视图
function updateGridView(locations, statistics) {
    const contentDiv = document.getElementById('hot-locations-content');

    if (locations.length > 0) {
        let gridHTML = '';

        // 如果有统计信息，显示AI分析覆盖率
        if (statistics) {
            gridHTML += `
                <div class="statistics-info">
                    <div class="stats-item">
                        <span class="stats-label">📊 总校园卡数：</span>
                        <span class="stats-value">${statistics.total_cards}</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-label">🤖 AI分析覆盖：</span>
                        <span class="stats-value">${statistics.cards_with_ai_analysis}/${statistics.total_cards} (${statistics.ai_analysis_coverage}%)</span>
                    </div>
                    <div class="stats-note">
                        <small>💡 统计基于AI分析后的标准地点名称，确保数据准确性</small>
                    </div>
                </div>
            `;
        }

        gridHTML += '<div class="hot-locations-grid"></div>';
        contentDiv.innerHTML = gridHTML;

        const gridDiv = contentDiv.querySelector('.hot-locations-grid');

        locations.forEach((location, index) => {
            // 根据排名添加不同的样式类
            let rankClass = '';
            if (index === 0) rankClass = 'rank-1';
            else if (index === 1) rankClass = 'rank-2';
            else if (index === 2) rankClass = 'rank-3';

            // 检查是否是原始数据（未经AI分析）
            const isOriginal = location.location.startsWith('[原始]');
            const displayLocation = isOriginal ? location.location.replace('[原始] ', '') : location.location;
            const originalClass = isOriginal ? 'original-data' : '';

            gridDiv.innerHTML += `
                <div class="hot-location-item ${rankClass} ${originalClass}" onclick="searchLocation('${displayLocation}')">
                    <div class="location-name">
                        ${isOriginal ? '📝 ' : '🤖 '}${displayLocation}
                        ${isOriginal ? '<span class="original-tag">原始</span>' : '<span class="ai-tag">AI标准</span>'}
                    </div>
                    <div class="location-count">${location.count}次</div>
                    <div class="location-stats">
                        <div class="location-percentage">占比 ${location.percentage}%</div>
                    </div>
                </div>
            `;
        });
    } else {
        contentDiv.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📍</div>
                <div class="empty-text">暂无热门地点数据</div>
                <div class="empty-hint">当有更多校园卡发现记录时，这里会显示热门地点</div>
            </div>
        `;
    }
}

// 切换图表类型
function switchChart(chartType) {
    currentChartType = chartType;

    // 更新按钮状态
    document.querySelectorAll('.chart-toggle-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');

    // 切换视图
    document.querySelectorAll('.chart-view').forEach(view => {
        view.classList.remove('active');
    });

    if (chartType === 'grid') {
        document.getElementById('hot-locations-grid').classList.add('active');
    } else {
        document.getElementById('hot-locations-charts').classList.add('active');
        updateChart(chartType);
    }
}

// 更新图表
function updateChart(chartType) {
    if (locationsData.length === 0) {
        document.getElementById('chart-legend').innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">📊</div>
                <div class="empty-text">暂无数据</div>
            </div>
        `;
        return;
    }

    const ctx = document.getElementById('locationsChart').getContext('2d');

    // 销毁现有图表
    if (locationsChart) {
        locationsChart.destroy();
    }

    // 准备数据
    const labels = locationsData.map(item => item.location);
    const data = locationsData.map(item => item.count);
    const percentages = locationsData.map(item => item.percentage);

    // 生成颜色
    const colors = generateColors(locationsData.length);

    // 创建新图表
    if (chartType === 'bar') {
        createBarChart(ctx, labels, data, colors);
    } else if (chartType === 'pie') {
        createPieChart(ctx, labels, data, colors);
    }

    // 更新图例
    updateChartLegend(labels, data, percentages, colors);
}

// 创建柱状图
function createBarChart(ctx, labels, data, colors) {
    locationsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '发现次数',
                data: data,
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('0.8', '1')),
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const percentage = locationsData[context.dataIndex].percentage;
                            return `${context.parsed.y}次 (${percentage}%)`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                }
            },
            onClick: (event, elements) => {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const location = locationsData[index].location;
                    searchLocation(location);
                }
            }
        }
    });
}

// 创建饼图
function createPieChart(ctx, labels, data, colors) {
    locationsChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderColor: '#fff',
                borderWidth: 3,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const percentage = locationsData[context.dataIndex].percentage;
                            return `${context.label}: ${context.parsed}次 (${percentage}%)`;
                        }
                    }
                }
            },
            onClick: (event, elements) => {
                if (elements.length > 0) {
                    const index = elements[0].index;
                    const location = locationsData[index].location;
                    searchLocation(location);
                }
            }
        }
    });
}

// 生成颜色数组
function generateColors(count) {
    const baseColors = [
        'rgba(255, 224, 178, 0.8)',  // 浅金色
        'rgba(225, 190, 231, 0.8)',  // 浅紫色
        'rgba(200, 230, 201, 0.8)',  // 浅绿色
        'rgba(187, 222, 251, 0.8)',  // 浅蓝色
        'rgba(255, 204, 188, 0.8)',  // 浅橙色
        'rgba(174, 213, 129, 0.8)',  // 浅青绿色
        'rgba(206, 147, 216, 0.8)',  // 浅紫罗兰色
        'rgba(239, 154, 154, 0.8)',  // 浅红色
        'rgba(255, 241, 118, 0.8)',  // 浅黄色
        'rgba(176, 190, 197, 0.8)'   // 浅灰蓝色
    ];

    return baseColors.slice(0, count);
}

// 更新图例
function updateChartLegend(labels, data, percentages, colors) {
    const legendDiv = document.getElementById('chart-legend');

    let legendHTML = '<h4>📊 数据详情</h4>';

    labels.forEach((label, index) => {
        legendHTML += `
            <div class="legend-item" onclick="searchLocation('${label}')" style="cursor: pointer;">
                <div style="display: flex; align-items: center; flex: 1;">
                    <div class="legend-color" style="background-color: ${colors[index]};"></div>
                    <span class="legend-label">${label}</span>
                </div>
                <div>
                    <span class="legend-value">${data[index]}次</span>
                    <span class="legend-percentage">${percentages[index]}%</span>
                </div>
            </div>
        `;
    });

    legendDiv.innerHTML = legendHTML;
}

// 搜索特定地点（点击热门地点时触发）
function searchLocation(location) {
    // 显示该地点的详细信息
    showLocationDetails(location);
}

// 显示地点详细信息
async function showLocationDetails(location) {
    try {
        // 获取该地点的所有校园卡信息
        const result = await apiCall('/query_lost_card?student_id=dummy');

        if (result.unmatched_cards && result.unmatched_cards.length > 0) {
            // 筛选出该地点的校园卡
            const locationCards = result.unmatched_cards.filter(card =>
                card.found_location && card.found_location.includes(location)
            );

            if (locationCards.length > 0) {
                let message = `📍 地点：${location}\n\n`;
                message += `共有 ${locationCards.length} 张校园卡在此地点被发现：\n\n`;

                locationCards.forEach((card, index) => {
                    message += `${index + 1}. 发现时间：${card.found_time}\n`;

                    // 根据是否有真实姓名显示不同的信息
                    if (card.owner_name && card.name_source === 'real') {
                        message += `   卡主姓名：${card.owner_name} (${card.masked_info.student_id})\n\n`;
                    } else {
                        message += `   持卡人：${card.masked_info.name} (${card.masked_info.student_id})\n\n`;
                    }
                });

                alert(message);
            } else {
                alert(`📍 地点：${location}\n\n该地点暂无未认领的校园卡`);
            }
        } else {
            alert(`📍 地点：${location}\n\n暂无相关校园卡信息`);
        }
    } catch (error) {
        alert(`📍 地点：${location}\n\n获取信息失败，请稍后重试`);
    }
}

// 加载公示列表
async function loadPublicList() {
    try {
        const result = await apiCall('/query_lost_card?student_id=dummy');
        const contentDiv = document.getElementById('public-list-content');

        if (result.unmatched_cards && result.unmatched_cards.length > 0) {
            contentDiv.innerHTML = '<h3>📋 未认领的校园卡</h3>';
            result.unmatched_cards.forEach(card => {
                // 根据处理方式设置不同的图标和样式
                const handlerIcon = card.handler_option === 1 ? '📞' : '📍';
                const handlerClass = card.handler_option === 1 ? 'contact-handler' : 'location-handler';

                // 根据是否有真实姓名显示不同的信息
                let ownerInfo = '';
                if (card.owner_name && card.name_source === 'real') {
                    ownerInfo = `<strong>卡主姓名（已作隐私保护）：</strong>${card.owner_name} (${card.masked_info.student_id})`;
                } else {
                    ownerInfo = `<strong>卡主信息（已作隐私保护）：</strong>${card.masked_info.name} (${card.masked_info.student_id})`;
                }

                contentDiv.innerHTML += `
                    <div class="result-item">
                        <p><strong>发现时间：</strong>${card.found_time}</p>
                        <p><strong>发现地点：</strong>${card.found_location}</p>
                        <p>${ownerInfo}</p>
                        <div class="handler-info ${handlerClass}">
                            <p><strong>${handlerIcon} 卡片处理方式：</strong>${card.handler_text}</p>
                            <p><strong>具体信息：</strong>${card.contact_info}</p>
                        </div>
                    </div>
                `;
            });
        } else {
            contentDiv.innerHTML = `
                <h3>📋 未认领的校园卡</h3>
                <div class="empty-state">
                    <div class="empty-icon">🎉</div>
                    <div class="empty-text">暂无未认领的校园卡</div>
                    <div class="empty-hint">所有校园卡都已被认领！</div>
                </div>
            `;
        }
    } catch (error) {
        document.getElementById('public-list-content').innerHTML = `
            <h3>📋 未认领的校园卡</h3>
            <div class="empty-state">
                <div class="empty-icon">❌</div>
                <div class="empty-text">加载失败</div>
                <div class="empty-hint">请稍后重试</div>
            </div>
        `;
    }
}

// 加载论坛帖子
async function loadForumPosts() {
    try {
        const posts = await apiCall('/forum/posts');
        const postsDiv = document.getElementById('forum-posts');
        
        if (posts.length > 0) {
            postsDiv.innerHTML = '';
            posts.forEach(post => {
                postsDiv.innerHTML += `
                    <div class="post-item ${post.is_ad ? 'ad' : ''}">
                        <div class="post-title">${post.title} ${post.is_ad ? '[广告]' : ''}</div>
                        <div class="post-meta">作者：${post.author_name} | 发布时间：${post.created_at}</div>
                        <div class="post-content">${post.content}</div>
                    </div>
                `;
            });
        } else {
            postsDiv.innerHTML = '<p>暂无帖子</p>';
        }
    } catch (error) {
        document.getElementById('forum-posts').innerHTML = '<p>加载失败</p>';
    }
}

// 发布帖子
document.getElementById('create-post-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    if (!currentUser) {
        alert('请先登录');
        return;
    }
    
    const title = document.getElementById('post-title').value;
    const content = document.getElementById('post-content').value;
    const isAd = document.getElementById('is-ad').checked;
    
    try {
        await apiCall('/forum/post', 'POST', {
            title: title,
            content: content,
            author_id: currentUser.user_id,
            is_ad: isAd
        });
        
        alert('帖子发布成功！');
        document.getElementById('create-post-form').reset();
        showForum();
    } catch (error) {
        // 错误已在apiCall中处理
    }
});

// 加载奖励列表
async function loadRewards() {
    try {
        const rewards = await apiCall('/rewards');
        const rewardsDiv = document.getElementById('rewards-list');
        
        if (rewards.length > 0) {
            rewardsDiv.innerHTML = '';
            rewards.forEach(reward => {
                const canRedeem = currentUser && currentUser.points >= reward.points_required;
                rewardsDiv.innerHTML += `
                    <div class="reward-item">
                        <div class="reward-info">
                            <h4>${reward.name}</h4>
                            <p>${reward.description}</p>
                        </div>
                        <div>
                            <span class="reward-points">${reward.points_required} 积分</span>
                            <button class="redeem-btn" ${!canRedeem ? 'disabled' : ''} 
                                    onclick="redeemReward(${reward.id}, ${reward.points_required})">
                                ${canRedeem ? '兑换' : '积分不足'}
                            </button>
                        </div>
                    </div>
                `;
            });
        } else {
            rewardsDiv.innerHTML = '<p>暂无奖励</p>';
        }
    } catch (error) {
        document.getElementById('rewards-list').innerHTML = '<p>加载失败</p>';
    }
}

// 兑换奖励
async function redeemReward(rewardId, pointsRequired) {
    if (!currentUser) {
        alert('请先登录');
        return;
    }
    
    if (currentUser.points < pointsRequired) {
        alert('积分不足');
        return;
    }
    
    try {
        const result = await apiCall('/reward/redeem', 'POST', {
            user_id: currentUser.user_id,
            reward_id: rewardId
        });
        
        // 显示兑换成功提示
        alert('恭喜您兑换成功，请您在两周内凭姓名和学号到兑换处领取奖品！');
        currentUser.points = result.remaining_points;
        updateUserInfo();
        loadRewards(); // 重新加载奖励列表
    } catch (error) {
        // 错误已在apiCall中处理
    }
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // 在查询页面按Escape清空查询
    if (e.key === 'Escape' && document.getElementById('query-card-page').classList.contains('active')) {
        clearQuery();
    }

    // Ctrl+Enter 快速查询
    if (e.ctrlKey && e.key === 'Enter' && document.getElementById('query-card-page').classList.contains('active')) {
        e.preventDefault();
        document.getElementById('query-card-form').dispatchEvent(new Event('submit'));
    }
});

// 智能地点查询功能
function clearSmartQuery() {
    document.getElementById('location-input').value = '';
    document.getElementById('smart-query-result').innerHTML = '';
    document.getElementById('smart-query-status').style.display = 'none';
    document.getElementById('location-input').focus();

    // 同时清空地图
    clearSmartQueryResults();
}

// 清空智能查询结果和地图
function clearSmartQueryResults() {
    // 清空查询结果区域
    document.getElementById('smart-query-result').innerHTML = '';

    // 隐藏地图区域
    const mapSection = document.getElementById('campus-map-section');
    if (mapSection) {
        mapSection.style.display = 'none';
    }

    // 清空地图标记
    const markersContainer = document.getElementById('map-markers');
    if (markersContainer) {
        markersContainer.innerHTML = '';
    }

    // 隐藏任何现有的地图提示框
    hideMapTooltip();
}

// 显示智能查询状态
function showSmartQueryStatus(type, message) {
    const statusDiv = document.getElementById('smart-query-status');
    statusDiv.className = `query-status ${type}`;
    statusDiv.textContent = message;

    // 确保状态元素可见
    statusDiv.style.display = 'block';

    if (type !== 'loading') {
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 5000);
    }
}

// 智能地点查询表单提交
document.getElementById('smart-location-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    const userInput = document.getElementById('location-input').value.trim();

    if (!userInput) {
        showSmartQueryStatus('error', '请输入您的查询内容');
        return;
    }

    // 清空之前的查询结果和地图
    clearSmartQueryResults();

    // 显示加载状态
    showSmartQueryStatus('loading', '🚀 正在快速分析您的查询...');

    try {
        const result = await apiCall('/smart_location_query', 'POST', {
            user_input: userInput
        });

        const resultDiv = document.getElementById('smart-query-result');

        if (result.success) {
            let resultHtml = `
                <div class="smart-result-header">
                    <h3>🎯 查询结果</h3>
                    <p class="parsing-info">
                        <strong>AI识别结果：</strong>${result.parsing_result.reasoning}
                        <span class="confidence-badge">置信度: ${(result.parsing_result.confidence * 100).toFixed(0)}%</span>
                    </p>
                </div>
            `;

            result.results.forEach((locationResult, index) => {
                resultHtml += `
                    <div class="smart-result-item">
                        <div class="location-header">
                            <h4>📍 ${locationResult.location}</h4>
                        </div>
                `;

                if (locationResult.nearest_lost_and_found) {
                    const lostFound = locationResult.nearest_lost_and_found;
                    resultHtml += `
                        <div class="nearest-point-info">
                            <div class="point-details">
                                <div class="point-name">🏢 最近招领点：${lostFound.name}</div>
                                <div class="point-distance">📏 距离：约 ${lostFound.distance} 个单位</div>
                                <div class="point-coordinates">📌 坐标：(${lostFound.coordinates.x}, ${lostFound.coordinates.y})</div>
                            </div>
                        </div>
                        <div class="ai-advice">
                            <div class="advice-header">🤖 AI建议：</div>
                            <div class="advice-content" id="ai-advice-${index}">
                                ${locationResult.ai_advice_loading ?
                                    '<div class="ai-loading">🤖 正在生成智能建议...</div>' :
                                    locationResult.ai_advice}
                            </div>
                        </div>
                    `;
                } else {
                    resultHtml += `
                        <div class="no-point-info">
                            <div class="advice-content">${locationResult.ai_advice}</div>
                        </div>
                    `;
                }

                resultHtml += `</div>`;
            });

            resultDiv.innerHTML = resultHtml;
            const statusMessage = result.results.length > 1
                ? `✅ 成功识别到 ${result.results.length} 个地点！已按语义相关性排序，最相关的地点显示在前面。`
                : `✅ 成功识别到 ${result.results.length} 个地点！`;
            showSmartQueryStatus('success', statusMessage);

            // 显示校园地图
            if (result.map_data && result.map_data.markers.length > 0) {
                displayCampusMap(result.map_data);
            }

            // 异步加载AI建议
            loadAIAdviceForResults(result.results);
        } else {
            resultDiv.innerHTML = `
                <div class="smart-result-item">
                    <div class="no-result">
                        <div class="no-result-icon">🤔</div>
                        <div class="no-result-message">${result.message}</div>
                        <div class="no-result-hint">
                            <p>💡 建议：</p>
                            <ul>
                                <li>尝试使用更具体的地点名称</li>
                                <li>可以描述您的具体需求，如"我在XX地点丢了东西"</li>
                                <li>参考校园地图上的建筑物名称</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            showSmartQueryStatus('error', '❌ 未能识别到有效地点');
        }

        // 滚动到结果区域
        document.getElementById('smart-query-result-section').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

    } catch (error) {
        showSmartQueryStatus('error', '❌ 查询失败，请稍后重试');
        document.getElementById('smart-query-result').innerHTML = `
            <div class="smart-result-item">
                <div class="error-result">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">查询过程中发生错误</div>
                    <div class="error-hint">请检查网络连接或稍后重试</div>
                </div>
            </div>
        `;
    }
});

// 异步加载AI建议
async function loadAIAdviceForResults(results) {
    for (let i = 0; i < results.length; i++) {
        const locationResult = results[i];

        // 只为有招领点的地点加载AI建议
        if (locationResult.nearest_lost_and_found && locationResult.ai_advice_loading) {
            try {
                const adviceElement = document.getElementById(`ai-advice-${i}`);
                if (adviceElement) {
                    // 显示加载动画
                    adviceElement.innerHTML = '<div class="ai-loading">🤖 正在生成智能建议<span class="loading-dots">...</span></div>';

                    // 调用AI建议接口
                    const adviceResult = await apiCall('/get_ai_advice', 'POST', {
                        location_name: locationResult.location,
                        nearest_point: locationResult.nearest_lost_and_found
                    });

                    if (adviceResult.success) {
                        // 流式显示AI建议
                        await typewriterEffect(adviceElement, adviceResult.ai_advice);
                    } else {
                        adviceElement.innerHTML = `<div class="ai-error">❌ AI建议生成失败，请稍后重试</div>`;
                    }
                }
            } catch (error) {
                console.error('AI建议加载失败:', error);
                const adviceElement = document.getElementById(`ai-advice-${i}`);
                if (adviceElement) {
                    adviceElement.innerHTML = `<div class="ai-error">❌ AI建议加载失败</div>`;
                }
            }
        }
    }
}

// 打字机效果显示AI建议
async function typewriterEffect(element, text) {
    element.innerHTML = '';
    const speed = 30; // 打字速度（毫秒）

    for (let i = 0; i < text.length; i++) {
        element.innerHTML += text.charAt(i);
        await new Promise(resolve => setTimeout(resolve, speed));
    }
}

// 显示校园地图
function displayCampusMap(mapData) {
    const mapSection = document.getElementById('campus-map-section');
    const markersContainer = document.getElementById('map-markers');

    // 显示地图区域
    mapSection.style.display = 'block';

    // 清空现有标记
    markersContainer.innerHTML = '';

    // 获取地图图片元素
    const mapImage = document.getElementById('campus-map-image');

    // 等待图片加载完成后添加标记
    if (mapImage.complete) {
        addMapMarkers(mapData.markers, markersContainer, mapImage);
    } else {
        mapImage.onload = function() {
            addMapMarkers(mapData.markers, markersContainer, mapImage);
        };
    }

    // 滚动到地图区域
    setTimeout(() => {
        mapSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }, 300);
}

// 添加地图标记
function addMapMarkers(markers, container, mapImage) {
    // 计算图片在容器中的实际尺寸和位置
    const scaleX = mapImage.offsetWidth / mapImage.naturalWidth;
    const scaleY = mapImage.offsetHeight / mapImage.naturalHeight;

    markers.forEach(marker => {
        // 计算标记在缩放后图片中的位置
        const x = marker.x * scaleX;
        const y = marker.y * scaleY;

        // 创建标记元素
        const markerElement = document.createElement('div');
        markerElement.className = `map-marker ${marker.shape} ${marker.color.replace('#', '')}`;
        markerElement.style.left = x + 'px';
        markerElement.style.top = y + 'px';
        markerElement.style.backgroundColor = marker.color;

        // 添加点击事件和悬停提示
        markerElement.title = marker.name + (marker.distance ? ` (距离: ${marker.distance}米)` : '');

        // 添加悬停效果
        markerElement.addEventListener('mouseenter', function(e) {
            showMapTooltip(e, marker);
        });

        markerElement.addEventListener('mouseleave', function() {
            hideMapTooltip();
        });

        // 添加点击事件
        markerElement.addEventListener('click', function() {
            alert(`📍 ${marker.name}\n坐标: (${marker.x}, ${marker.y})${marker.distance ? `\n距离: ${marker.distance}米` : ''}`);
        });

        container.appendChild(markerElement);
    });
}

// 显示地图提示框
function showMapTooltip(event, marker) {
    // 移除现有提示框
    hideMapTooltip();

    const tooltip = document.createElement('div');
    tooltip.className = 'map-tooltip';
    tooltip.id = 'map-tooltip';

    let tooltipText = marker.name;
    if (marker.distance) {
        tooltipText += `\n距离: ${marker.distance}米`;
    }
    tooltipText += `\n坐标: (${marker.x}, ${marker.y})`;

    tooltip.textContent = tooltipText;
    tooltip.style.whiteSpace = 'pre-line';

    // 定位提示框
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = (rect.left + rect.width / 2) + 'px';
    tooltip.style.top = rect.top + 'px';

    document.body.appendChild(tooltip);
}

// 隐藏地图提示框
function hideMapTooltip() {
    const tooltip = document.getElementById('map-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    showLogin(); // 默认显示登录页面

    // 初始化查询历史
    updateHistoryDisplay();

    // 启动连接保持机制
    startKeepAlive();
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    stopKeepAlive();
});
