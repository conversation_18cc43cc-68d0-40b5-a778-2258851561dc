/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 50%, #e8f4f8 100%);
    min-height: 100vh;
    color: #2c3e50;
}

/* 所有页面背景样式 */
.page {
    background:
        linear-gradient(135deg, rgba(227, 242, 253, 0.65) 0%, rgba(240, 248, 255, 0.65) 50%, rgba(232, 244, 248, 0.65) 100%),
        url('img1.png') center/cover no-repeat;
    background-attachment: fixed;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 页面切换 */
.page {
    display: none;
    min-height: 100vh;
}

.page.active {
    display: block;
}

/* 表单容器 */
.form-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    max-width: 400px;
    margin: 50px auto;
}

.form-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
    font-size: 28px;
    font-weight: 600;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e3f2fd;
    border-radius: 6px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #64b5f6;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.1);
}

/* 选择框特殊样式 */
.form-group select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2364b5f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.radio-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.radio-group input[type="radio"] {
    width: auto;
    margin-right: 8px;
}

/* 按钮样式 */
button {
    background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(100, 181, 246, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(100, 181, 246, 0.65);
    background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
}

.form-container button {
    width: 100%;
    padding: 15px;
    font-size: 18px;
}

/* 注册按钮特殊样式 - 绿色 */
#register-form button[type="submit"] {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

#register-form button[type="submit"]:hover {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.65);
}

/* 切换链接 */
.switch-link {
    text-align: center;
    margin-top: 20px;
    color: #666;
}

.switch-link a {
    color: #42a5f5;
    text-decoration: none;
    font-weight: 600;
}

.switch-link a:hover {
    color: #2196f3;
    text-decoration: underline;
}

/* 首页样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

header h1 {
    color: #2c3e50;
    font-size: 32px;
    font-weight: 700;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 600;
}

.user-info button {
    padding: 8px 16px;
    font-size: 14px;
}

/* 菜单网格 */
.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.menu-item {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.menu-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(100, 181, 246, 0.15);
    border-color: rgba(100, 181, 246, 0.3);
}

.menu-icon {
    font-size: 56px;
    margin-bottom: 15px;
    filter: drop-shadow(0 2px 4px rgba(100, 181, 246, 0.3));
}

.menu-item h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: 600;
}

.menu-item p {
    color: #546e7a;
    font-size: 14px;
}

/* 页面头部 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    flex-wrap: wrap;
    gap: 15px;
}

/* 时间过滤提示样式 */
.time-filter-notice {
    padding: 6px 12px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 15px;
    color: #856404;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.back-btn {
    background: linear-gradient(135deg, #90a4ae 0%, #78909c 100%);
    padding: 8px 16px;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(144, 164, 174, 0.3);
}

.back-btn:hover {
    background: linear-gradient(135deg, #78909c 0%, #607d8b 100%);
}

.create-post-btn {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    padding: 8px 16px;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(102, 187, 106, 0.3);
}

.create-post-btn:hover {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.points-display {
    background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(100, 181, 246, 0.3);
}

/* 内容区域 */
.page .container > form,
.page .container > div:not(.page-header) {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

/* 结果显示 */
.result-item {
    background: rgba(227, 242, 253, 0.5);
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #64b5f6;
    border: 1px solid rgba(100, 181, 246, 0.2);
}

.result-item h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 600;
}

.result-item p {
    color: #546e7a;
    margin-bottom: 5px;
}

/* 帖子样式 */
.post-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.post-item.ad {
    border-left-color: #ffc107;
    background: #fff3cd;
}

.post-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.post-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.post-content {
    color: #555;
    line-height: 1.6;
}

/* 奖励项目 */
.reward-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reward-info h4 {
    color: #333;
    margin-bottom: 5px;
}

.reward-info p {
    color: #666;
    font-size: 14px;
}

.reward-points {
    background: #667eea;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 600;
    margin-right: 10px;
}

.redeem-btn {
    padding: 8px 16px;
    font-size: 14px;
    background: #28a745;
}

.redeem-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* 文件上传样式 */
input[type="file"] {
    padding: 8px;
    border: 2px dashed #e1e5e9;
    border-radius: 6px;
    background: #f8f9fa;
    cursor: pointer;
    transition: border-color 0.3s;
}

input[type="file"]:hover {
    border-color: #667eea;
}

input[type="file"]:focus {
    outline: none;
    border-color: #667eea;
}

/* 图片预览样式 */
.photo-preview {
    margin-top: 10px;
    text-align: center;
}

.photo-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    object-fit: cover;
}

.photo-preview .preview-info {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.photo-preview .remove-photo {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-top: 5px;
}

.photo-preview .remove-photo:hover {
    background: #c82333;
}

/* 查询页面样式 */
.query-form-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.input-with-button {
    display: flex;
    gap: 10px;
    align-items: center;
}

.input-with-button input {
    flex: 1;
    margin-bottom: 0;
}

.input-with-button button {
    padding: 12px 20px;
    font-size: 14px;
    white-space: nowrap;
}

#clear-query-btn {
    background: linear-gradient(135deg, #90a4ae 0%, #78909c 100%);
    box-shadow: 0 2px 8px rgba(144, 164, 174, 0.3);
}

#clear-query-btn:hover {
    background: linear-gradient(135deg, #78909c 0%, #607d8b 100%);
}

.query-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 6px;
    font-weight: 600;
    text-align: center;
    display: none;
}

.query-status.loading {
    background: #e3f2fd;
    color: #1976d2;
    display: block;
}

.query-status.success {
    background: #e8f5e8;
    color: #2e7d32;
    display: block;
}

.query-status.error {
    background: #ffebee;
    color: #c62828;
    display: block;
}

.query-result-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-height: 100px;
}

.query-result-section:empty {
    display: none;
}

.query-history-section {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.query-history-section h3 {
    color: #333;
    margin-bottom: 15px;
    border-bottom: 2px solid #e1e5e9;
    padding-bottom: 10px;
}

.history-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
    cursor: pointer;
    transition: background-color 0.3s;
}

.history-item:hover {
    background: #e9ecef;
}

.history-item .history-query {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.history-item .history-time {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.history-item .history-result {
    font-size: 14px;
    color: #555;
}

.clear-history-btn {
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    margin-top: 15px;
}

.clear-history-btn:hover {
    background: #c82333;
}

/* 联系方式字段样式 */
.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

#contact-field {
    border-left: 3px solid #64b5f6;
    padding-left: 15px;
    background: rgba(227, 242, 253, 0.3);
    border-radius: 0 6px 6px 0;
}

#contact-field input[type="tel"] {
    border-color: #64b5f6;
}

#contact-field input[type="tel"]:focus {
    border-color: #42a5f5;
    box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.15);
}

/* 查询结果中的联系信息样式 */
.contact-info {
    background: rgba(232, 245, 233, 0.65);
    border: 1px solid #81c784;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    backdrop-filter: blur(5px);
}

.contact-info .contact-label {
    font-weight: 600;
    color: #2e7d32;
    margin-bottom: 5px;
}

.contact-info .contact-value {
    font-size: 18px;
    color: #1b5e20;
    font-family: monospace;
    letter-spacing: 1px;
}

.location-info {
    background: rgba(255, 243, 224, 0.65);
    border: 1px solid #ffb74d;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    backdrop-filter: blur(5px);
}

.location-info .location-label {
    font-weight: 600;
    color: #f57c00;
    margin-bottom: 5px;
}

.location-info .location-value {
    font-size: 16px;
    color: #e65100;
}

/* 公示列表中的处理方式信息样式 */
.handler-info {
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 6px;
    margin-top: 12px;
    border: 1px solid #e3f2fd;
    transition: all 0.3s ease;
}

.handler-info.contact-handler {
    border-left: 4px solid #4caf50;
    background: rgba(76, 175, 80, 0.05);
}

.handler-info.location-handler {
    border-left: 4px solid #ff9800;
    background: rgba(255, 152, 0, 0.05);
}

.handler-info p {
    margin: 6px 0;
    font-size: 14px;
}

.handler-info strong {
    color: #2c3e50;
}

.handler-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 热门地点样式 */
.hot-locations-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.hot-locations-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    border-bottom: 2px solid #64b5f6;
    padding-bottom: 10px;
    font-size: 20px;
    font-weight: 600;
}

.hot-locations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.hot-location-item {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #2c3e50;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(100, 181, 246, 0.2);
    box-shadow: 0 4px 12px rgba(100, 181, 246, 0.15);
}

.hot-location-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(100, 181, 246, 0.25);
    background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
}

.hot-location-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
    opacity: 0;
}

.hot-location-item:hover::before {
    opacity: 1;
    top: -100%;
    left: -100%;
}

/* 统计信息样式 */
.statistics-info {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid rgba(100, 181, 246, 0.2);
    backdrop-filter: blur(10px);
}

.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.stats-label {
    font-weight: 600;
    color: #2c3e50;
}

.stats-value {
    font-weight: 700;
    color: #1976d2;
    font-size: 16px;
}

.stats-note {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(100, 181, 246, 0.2);
    text-align: center;
}

.stats-note small {
    color: #546e7a;
    font-style: italic;
}

/* 地点标签样式 */
.ai-tag, .original-tag {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
    font-weight: 600;
}

.ai-tag {
    background: rgba(76, 175, 80, 0.2);
    color: #2e7d32;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.original-tag {
    background: rgba(255, 152, 0, 0.2);
    color: #ef6c00;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

/* 原始数据项样式 */
.hot-location-item.original-data {
    border-left: 4px solid #ff9800;
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.hot-location-item.original-data:hover {
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
}

/* 校园地图样式 */
.campus-map-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 8px;
    margin-top: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.campus-map-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.map-container {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.map-wrapper {
    position: relative;
    flex: 1;
    border: 2px solid rgba(100, 181, 246, 0.3);
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    /* 确保容器尺寸稳定 */
    min-height: 300px;
}

#campus-map-image {
    width: 100%;
    height: auto;
    display: block;
    max-width: 800px;
    /* 防止图片在加载过程中尺寸跳动 */
    min-height: 300px;
    object-fit: contain;
    /* 确保图片完全加载后再显示 */
    opacity: 0;
    transition: opacity 0.3s ease;
}

#campus-map-image.loaded {
    opacity: 1;
}

#map-markers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    /* 确保标记层与图片完全重叠 */
    z-index: 1;
}

.map-marker {
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: 10;
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: markerPulse 2s infinite;
}

.map-marker:hover {
    transform: translate(-50%, -50%) scale(1.3);
    z-index: 20;
    animation: markerGlow 0.5s infinite alternate;
}

.map-marker.square {
    width: 20px;
    height: 20px;
    border: 3px solid white;
    box-shadow:
        0 0 0 2px rgba(33, 150, 243, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(33, 150, 243, 0.6);
    position: relative;
}

.map-marker.square::before {
    content: '📍';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    line-height: 1;
}

.map-marker.circle {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow:
        0 0 0 2px rgba(76, 175, 80, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(76, 175, 80, 0.6);
    position: relative;
}

.map-marker.circle::before {
    content: '🎯';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    line-height: 1;
}

/* 标记动画效果 */
@keyframes markerPulse {
    0% {
        box-shadow:
            0 0 0 0 rgba(33, 150, 243, 0.7),
            0 4px 12px rgba(0, 0, 0, 0.4);
    }
    50% {
        box-shadow:
            0 0 0 8px rgba(33, 150, 243, 0.2),
            0 4px 12px rgba(0, 0, 0, 0.4);
    }
    100% {
        box-shadow:
            0 0 0 0 rgba(33, 150, 243, 0),
            0 4px 12px rgba(0, 0, 0, 0.4);
    }
}

@keyframes markerGlow {
    0% {
        filter: brightness(1) drop-shadow(0 0 5px currentColor);
    }
    100% {
        filter: brightness(1.2) drop-shadow(0 0 15px currentColor);
    }
}

.map-marker.blue {
    background-color: #000000;
    animation: markerPulseBlack 2s infinite;
}

.map-marker.green {
    background-color: #dc3545;
    animation: markerPulseRed 2s infinite;
}

/* 黑色标记脉冲动画 */
@keyframes markerPulseBlack {
    0% {
        box-shadow:
            0 0 0 3px white,
            0 0 0 0 rgba(0, 0, 0, 0.8),
            0 4px 12px rgba(0, 0, 0, 0.5),
            0 0 20px rgba(0, 0, 0, 0.7);
    }
    50% {
        box-shadow:
            0 0 0 3px white,
            0 0 0 10px rgba(0, 0, 0, 0.3),
            0 4px 12px rgba(0, 0, 0, 0.5),
            0 0 25px rgba(0, 0, 0, 0.9);
    }
    100% {
        box-shadow:
            0 0 0 3px white,
            0 0 0 0 rgba(0, 0, 0, 0),
            0 4px 12px rgba(0, 0, 0, 0.5),
            0 0 20px rgba(0, 0, 0, 0.7);
    }
}

/* 红色标记脉冲动画 */
@keyframes markerPulseRed {
    0% {
        box-shadow:
            0 0 0 3px white,
            0 0 0 0 rgba(220, 53, 69, 0.8),
            0 4px 12px rgba(0, 0, 0, 0.4),
            0 0 20px rgba(220, 53, 69, 0.7);
    }
    50% {
        box-shadow:
            0 0 0 3px white,
            0 0 0 10px rgba(220, 53, 69, 0.3),
            0 4px 12px rgba(0, 0, 0, 0.4),
            0 0 25px rgba(220, 53, 69, 0.9);
    }
    100% {
        box-shadow:
            0 0 0 3px white,
            0 0 0 0 rgba(220, 53, 69, 0),
            0 4px 12px rgba(0, 0, 0, 0.4),
            0 0 20px rgba(220, 53, 69, 0.7);
    }
}

.map-legend {
    min-width: 150px;
    background: rgba(248, 249, 250, 0.95);
    padding: 15px;
    border-radius: 6px;
    border: 1px solid rgba(100, 181, 246, 0.2);
}

.map-legend h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #546e7a;
}

.legend-marker {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    position: relative;
    flex-shrink: 0;
}

.legend-marker.square {
    border-radius: 0;
}

.legend-marker.square::before {
    content: '📍';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    line-height: 1;
}

.legend-marker.circle {
    border-radius: 50%;
}

.legend-marker.circle::before {
    content: '🎯';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    line-height: 1;
}

.legend-marker.blue {
    background-color: #000000;
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.5),
        0 2px 6px rgba(0, 0, 0, 0.4);
}

.legend-marker.green {
    background-color: #dc3545;
    box-shadow:
        0 0 0 1px rgba(220, 53, 69, 0.4),
        0 2px 6px rgba(0, 0, 0, 0.3);
}

.map-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 30;
    pointer-events: none;
    transform: translate(-50%, -100%);
    margin-top: -8px;
}

.map-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .map-container {
        flex-direction: column;
    }

    .map-legend {
        min-width: auto;
        width: 100%;
    }
}

.location-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.location-stats {
    font-size: 12px;
    opacity: 0.9;
}

.location-count {
    font-size: 18px;
    font-weight: bold;
    margin: 5px 0;
}

.location-percentage {
    font-size: 11px;
    opacity: 0.8;
}

/* 公示列表内容样式 */
.public-list-content {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.public-list-content h3 {
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
    font-size: 20px;
}

/* 热门地点排名样式 */
.hot-location-item.rank-1 {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    color: #e65100;
    border: 2px solid #ffb74d;
    box-shadow: 0 6px 16px rgba(255, 183, 77, 0.3);
}

.hot-location-item.rank-1::after {
    content: '👑';
    position: absolute;
    top: 8px;
    right: 12px;
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(255, 183, 77, 0.5));
}

.hot-location-item.rank-2 {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    color: #4a148c;
    border: 2px solid #ce93d8;
    box-shadow: 0 6px 16px rgba(206, 147, 216, 0.3);
}

.hot-location-item.rank-3 {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #1b5e20;
    border: 2px solid #81c784;
    box-shadow: 0 6px 16px rgba(129, 199, 132, 0.3);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-state .empty-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state .empty-text {
    font-size: 16px;
    margin-bottom: 10px;
}

.empty-state .empty-hint {
    font-size: 14px;
    opacity: 0.7;
}

/* 图表相关样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.section-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
}

.chart-controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.chart-toggle-btn {
    background: rgba(255, 255, 255, 0.8);
    color: #546e7a;
    border: 2px solid #e3f2fd;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    backdrop-filter: blur(5px);
}

.chart-toggle-btn:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #64b5f6;
    color: #2c3e50;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(100, 181, 246, 0.2);
}

.chart-toggle-btn.active {
    background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    color: white;
    border-color: #42a5f5;
    box-shadow: 0 4px 16px rgba(100, 181, 246, 0.65);
}

.chart-view {
    display: none;
}

.chart-view.active {
    display: block;
}

.chart-container {
    background: rgba(227, 242, 253, 0.3);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: inset 0 2px 8px rgba(100, 181, 246, 0.1);
    border: 1px solid rgba(100, 181, 246, 0.2);
    position: relative;
    height: 400px;
}

.chart-container canvas {
    max-height: 100%;
    width: 100% !important;
    height: 100% !important;
}

.chart-legend {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(100, 181, 246, 0.2);
    backdrop-filter: blur(10px);
}

.chart-legend h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.legend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.legend-item:last-child {
    border-bottom: none;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 10px;
    flex-shrink: 0;
}

.legend-label {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

.legend-value {
    font-weight: 600;
    color: #42a5f5;
}

.legend-percentage {
    font-size: 12px;
    color: #546e7a;
    margin-left: 8px;
}

/* 响应式图表样式 */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .chart-controls {
        justify-content: center;
    }

    .chart-toggle-btn {
        flex: 1;
        min-width: 0;
        font-size: 12px;
        padding: 6px 12px;
    }

    .chart-container {
        height: 300px;
        padding: 15px;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .form-container {
        margin: 20px auto;
        padding: 20px;
    }
    
    .menu-grid {
        grid-template-columns: 1fr;
    }
    
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* 智能地点查询样式 */
.smart-query-form-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.smart-query-intro {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #64b5f6;
}

.smart-query-intro h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.smart-query-intro p {
    color: #546e7a;
    margin-bottom: 8px;
    line-height: 1.6;
}

.smart-query-intro em {
    color: #42a5f5;
    font-style: normal;
    font-weight: 600;
    background: rgba(100, 181, 246, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.form-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.form-actions button {
    flex: 1;
    max-width: 200px;
}

#clear-smart-query-btn {
    background: linear-gradient(135deg, #90a4ae 0%, #78909c 100%);
    box-shadow: 0 2px 8px rgba(144, 164, 174, 0.3);
}

#clear-smart-query-btn:hover {
    background: linear-gradient(135deg, #78909c 0%, #607d8b 100%);
}

.smart-query-result-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    min-height: 100px;
}

.smart-result-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e3f2fd;
}

.smart-result-header h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: 600;
}

.parsing-info {
    color: #546e7a;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.confidence-badge {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.smart-result-item {
    background: rgba(227, 242, 253, 0.3);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid rgba(100, 181, 246, 0.2);
    box-shadow: 0 4px 16px rgba(100, 181, 246, 0.1);
}

.location-header h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.nearest-point-info {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #4caf50;
}

.point-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.point-name, .point-distance, .point-coordinates {
    color: #2c3e50;
    font-weight: 500;
}

.point-name {
    font-size: 16px;
    font-weight: 600;
    color: #4caf50;
}

.ai-advice {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 6px;
    border-left: 4px solid #ff9800;
}

.advice-header {
    color: #ff9800;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 14px;
}

.advice-content {
    color: #2c3e50;
    line-height: 1.6;
    font-size: 14px;
    min-height: 20px;
}

/* AI建议加载状态 */
.ai-loading {
    color: #ff9800;
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 5px;
}

.loading-dots {
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    80%, 100% { opacity: 0; }
}

/* AI建议错误状态 */
.ai-error {
    color: #f44336;
    font-style: italic;
    font-size: 13px;
}

.no-result, .error-result {
    text-align: center;
    padding: 30px;
}

.no-result-icon, .error-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.no-result-message, .error-message {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
}

.no-result-hint, .error-hint {
    color: #546e7a;
    font-size: 14px;
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
}

.no-result-hint ul {
    margin-top: 10px;
    padding-left: 20px;
}

.no-result-hint li {
    margin-bottom: 5px;
}

.no-point-info {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 6px;
    border-left: 4px solid #ff5722;
}

/* 简洁的复选框样式 */
.checkbox-label {
    display: flex !important; /* 覆盖form-group label的block样式 */
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    margin: 10px 0;
    margin-bottom: 8px; /* 保持与其他label一致的下边距 */
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    transform: scale(1.1);
    width: auto; /* 覆盖form-group input的100%宽度 */
}

/* 智能查询响应式样式 */
@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }

    .form-actions button {
        max-width: none;
    }

    .parsing-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .point-details {
        font-size: 14px;
    }

    .smart-query-intro {
        padding: 15px;
    }

    .smart-result-item {
        padding: 20px;
    }

    .result-item.my-card::before {
        font-size: 11px;
        padding: 3px 6px;
    }

    .delete-card-btn {
        font-size: 13px;
        padding: 6px 12px;
    }
}

/* 用户自己的卡片样式 */
.result-item.my-card {
    border: 2px solid #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    position: relative;
}

.result-item.my-card::before {
    content: "👤 我的卡片";
    position: absolute;
    top: -1px;
    right: -1px;
    background: #28a745;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 0 8px 0 8px;
    font-weight: 600;
}

/* 卡片操作区域 */
.card-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(40, 167, 69, 0.2);
    text-align: center;
}

/* 删除按钮样式 */
.delete-card-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-card-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.delete-card-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

.delete-card-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.delete-card-btn:disabled:hover {
    background: #6c757d;
    transform: none;
    box-shadow: none;
}
