
from app import app
from waitress import serve

if __name__ == '__main__':
    print("正在启动 Waitress 服务器...")
    print("服务地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务")
    
    serve(
        app,
        host='0.0.0.0',
        port=5000,
        threads=10,
        connection_limit=1000,
        cleanup_interval=30,
        channel_timeout=3600,  # 增加到1小时
        socket_timeout=300,    # 添加socket超时设置
        log_socket_errors=True,
        send_bytes=18000,      # 增加发送缓冲区
        recv_bytes=65536       # 增加接收缓冲区
    )
